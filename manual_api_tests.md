# Manual API Tests for Get Template by Country and Party Code

This document provides manual test cases for the `GET /api/v1/templates/{country_code}/{party_code}` endpoint.

## Prerequisites

1. Start the API server: `go run main.go`
2. Ensure MongoDB is running with test data
3. Server should be running on `http://localhost:8080`

## Test Cases

### 1. Successful Template Retrieval - US Template

**Request:**
```bash
curl -X GET "http://localhost:8080/api/v1/templates/US/ABC123" \
  -H "Accept: application/json"
```

**Expected Response:** `200 OK`
```json
{
  "template_id": "uuid-string",
  "connection_id": "uuid-string",
  "is_default": false,
  "body": "US EV Charging Receipt",
  "header_text": {
    "font": "Arial",
    "style": "bold",
    "color": "#000000",
    "value": "🇺🇸 US EV Charging Station"
  },
  "header_logo": {
    "src": "https://us-charging.example.com/logo.png",
    "width": 200,
    "height": 100
  },
  "footer_text": {
    "font": "Arial",
    "style": "normal",
    "color": "#666666",
    "value": "Thank you for charging with us!"
  },
  "connection": {
    "connection_name": "US Charging Network",
    "country_code": "US",
    "party_code": "ABC123",
    "cpo_url": "https://us-charging.example.com"
  },
  "last_updated": "2023-10-29T12:00:00Z"
}
```

### 2. Case Insensitive Matching - Lowercase

**Request:**
```bash
curl -X GET "http://localhost:8080/api/v1/templates/us/abc123" \
  -H "Accept: application/json"
```

**Expected Response:** `200 OK` (Same template as above)

### 3. German Template Retrieval

**Request:**
```bash
curl -X GET "http://localhost:8080/api/v1/templates/DE/XYZ789" \
  -H "Accept: application/json"
```

**Expected Response:** `200 OK`
```json
{
  "template_id": "uuid-string",
  "connection_id": "uuid-string",
  "is_default": false,
  "body": "German EV Charging Receipt",
  "header_text": {
    "font": "Arial",
    "style": "bold",
    "color": "#000000",
    "value": "🇩🇪 Deutsche Ladestation"
  },
  "connection": {
    "connection_name": "German Charging Network",
    "country_code": "DE",
    "party_code": "XYZ789",
    "cpo_url": "https://de-charging.example.com"
  }
}
```

### 4. Mixed Case Input

**Request:**
```bash
curl -X GET "http://localhost:8080/api/v1/templates/De/xyz789" \
  -H "Accept: application/json"
```

**Expected Response:** `200 OK` (Same German template)

### 5. French Template Retrieval

**Request:**
```bash
curl -X GET "http://localhost:8080/api/v1/templates/FR/DEF456" \
  -H "Accept: application/json"
```

**Expected Response:** `200 OK`
```json
{
  "template_id": "uuid-string",
  "connection_id": "uuid-string",
  "is_default": false,
  "body": "French EV Charging Receipt",
  "header_text": {
    "font": "Arial",
    "style": "bold",
    "color": "#000000",
    "value": "🇫🇷 Station de Recharge Française"
  },
  "connection": {
    "connection_name": "French Charging Network",
    "country_code": "FR",
    "party_code": "DEF456",
    "cpo_url": "https://fr-charging.example.com"
  }
}
```

### 6. Template Not Found

**Request:**
```bash
curl -X GET "http://localhost:8080/api/v1/templates/XX/NOTFOUND" \
  -H "Accept: application/json"
```

**Expected Response:** `404 Not Found`
```
template not found
```

### 7. Invalid Country Code - Too Short

**Request:**
```bash
curl -X GET "http://localhost:8080/api/v1/templates/X/ABC123" \
  -H "Accept: application/json"
```

**Expected Response:** `400 Bad Request`
```
invalid country code
```

### 8. Empty Party Code

**Request:**
```bash
curl -X GET "http://localhost:8080/api/v1/templates/US/" \
  -H "Accept: application/json"
```

**Expected Response:** `400 Bad Request`
```
invalid party code
```

### 9. Special Characters in Party Code

**Request:**
```bash
curl -X GET "http://localhost:8080/api/v1/templates/US/ABC-123" \
  -H "Accept: application/json"
```

**Expected Response:** Depends on data - either `200 OK` if exists or `404 Not Found`

### 10. Numeric Country Code (Invalid)

**Request:**
```bash
curl -X GET "http://localhost:8080/api/v1/templates/12/ABC123" \
  -H "Accept: application/json"
```

**Expected Response:** `404 Not Found` (no template should match)

## Postman Collection

You can import these tests into Postman by creating a new collection with the following requests:

1. **US Template Success**
   - Method: GET
   - URL: `{{base_url}}/api/v1/templates/US/ABC123`
   - Headers: `Accept: application/json`

2. **Case Insensitive**
   - Method: GET
   - URL: `{{base_url}}/api/v1/templates/us/abc123`
   - Headers: `Accept: application/json`

3. **German Template**
   - Method: GET
   - URL: `{{base_url}}/api/v1/templates/DE/XYZ789`
   - Headers: `Accept: application/json`

4. **Not Found**
   - Method: GET
   - URL: `{{base_url}}/api/v1/templates/XX/NOTFOUND`
   - Headers: `Accept: application/json`

5. **Invalid Country Code**
   - Method: GET
   - URL: `{{base_url}}/api/v1/templates/X/ABC123`
   - Headers: `Accept: application/json`

Set the environment variable:
- `base_url`: `http://localhost:8080`

## Test Data Setup

Before running these tests, ensure your database contains the following test templates:

1. **US Template**: Country=US, Party=ABC123
2. **German Template**: Country=DE, Party=XYZ789  
3. **French Template**: Country=FR, Party=DEF456

You can use the `cmd/manual_test_country_party/main.go` script to set up test data automatically.

## Validation Checklist

- [ ] Successful template retrieval returns 200 with complete template data
- [ ] Case-insensitive matching works for both country and party codes
- [ ] Non-existent templates return 404 with appropriate error message
- [ ] Invalid country codes (< 2 characters) return 400
- [ ] Empty party codes return 400
- [ ] Response format matches OpenAPI specification
- [ ] All required fields are present in successful responses
- [ ] Error responses contain meaningful error messages
- [ ] Performance is acceptable (< 100ms for simple queries)

## Notes

- The endpoint supports case-insensitive matching using MongoDB regex queries
- Country codes should follow ISO 3166-1 alpha-2 standard (2 characters)
- Party codes must be at least 1 character long
- The endpoint is designed for high-frequency lookups in EV charging scenarios
