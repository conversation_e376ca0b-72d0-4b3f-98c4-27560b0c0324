# Manual Tests for RetrieveTemplateByCountryAndParty Endpoint

This directory contains comprehensive manual tests for the newly added `RetrieveTemplateByCountryAndParty` endpoint.

## Overview

The `GET /api/v1/templates/{country_code}/{party_code}` endpoint allows retrieving EV charging receipt templates by country code and party code with case-insensitive matching.

## Test Files

### 1. Full Integration Test with MongoDB
**File:** `cmd/manual_test_country_party/main.go`

- Uses Docker to spin up a MongoDB container
- Tests all layers: HTTP → Service → Repository → Database
- Inserts real test data and verifies end-to-end functionality
- Tests case-insensitive matching at the database level

**Run:**
```bash
go run cmd/manual_test_country_party/main.go
```

### 2. Quick Mock-based Test
**File:** `cmd/quick_test_endpoint/main.go`

- Uses mock services for fast testing
- Tests HTTP handler logic without database dependencies
- Verifies request/response handling and error cases
- Good for development and CI/CD pipelines

**Run:**
```bash
go run cmd/quick_test_endpoint/main.go
```

### 3. <PERSON> Script for API Testing
**File:** `run_manual_tests.sh`

- Bash script using curl to test live API endpoints
- Tests against running server instance
- Provides colored output and test summaries
- Good for manual verification and deployment testing

**Run:**
```bash
./run_manual_tests.sh [base_url]
# Example: ./run_manual_tests.sh http://localhost:8080
```

### 4. API Documentation and Examples
**File:** `manual_api_tests.md`

- Comprehensive curl examples for all test cases
- Postman collection setup instructions
- Expected request/response examples
- Validation checklist for manual testing

## Test Scenarios Covered

### ✅ Successful Cases
- Exact match retrieval (US/ABC123, DE/XYZ789, FR/DEF456)
- Case-insensitive matching (us/abc123 → US/ABC123)
- Mixed case input (De/xyz789 → DE/XYZ789)

### ✅ Error Cases
- Template not found (404)
- Invalid country code - too short (400)
- Empty party code (400)
- Internal service errors (500)

### ✅ Edge Cases
- Special characters in party codes
- Numeric country codes
- Unicode characters in template data

## Quick Start

1. **Start the API server:**
   ```bash
   go run main.go
   ```

2. **Run quick mock tests:**
   ```bash
   go run cmd/quick_test_endpoint/main.go
   ```

3. **Run API tests against live server:**
   ```bash
   ./run_manual_tests.sh
   ```

4. **Run full integration tests:**
   ```bash
   go run cmd/manual_test_country_party/main.go
   ```

## Test Data

The tests use the following sample templates:

| Country | Party Code | Connection Name | Description |
|---------|------------|-----------------|-------------|
| US | ABC123 | US Charging Network | American EV charging template |
| DE | XYZ789 | German Charging Network | German EV charging template |
| FR | DEF456 | French Charging Network | French EV charging template |

## Expected Behavior

### Case-Insensitive Matching
The endpoint supports case-insensitive matching for both country codes and party codes:
- `US/ABC123` = `us/abc123` = `Us/AbC123`
- `DE/XYZ789` = `de/xyz789` = `De/Xyz789`

### Input Validation
- Country codes must be at least 2 characters (ISO 3166-1 alpha-2)
- Party codes must be at least 1 character
- Invalid inputs return 400 Bad Request

### Response Format
Successful responses (200) return complete template data including:
- Template metadata (ID, connection ID, default flag)
- Styling information (header text, logos, footer text)
- Connection details (name, country, party, CPO URL)
- Last updated timestamp

## Integration with CI/CD

The mock-based test (`cmd/quick_test_endpoint/main.go`) can be integrated into CI/CD pipelines:

```yaml
# Example GitHub Actions step
- name: Run Manual Endpoint Tests
  run: go run cmd/quick_test_endpoint/main.go
```

## Troubleshooting

### Server Not Running
If tests fail with connection errors:
1. Ensure the API server is running: `go run main.go`
2. Check the server is listening on the expected port (default: 8080)
3. Verify health endpoint: `curl http://localhost:8080/health`

### Database Issues
For MongoDB-related test failures:
1. Ensure Docker is running for container-based tests
2. Check MongoDB connection string in configuration
3. Verify test data was inserted correctly

### Mock Test Failures
If mock tests fail:
1. Check that domain types match expected structure
2. Verify mock expectations are set up correctly
3. Ensure error types match domain error definitions

## Contributing

When adding new test cases:
1. Add to all relevant test files for consistency
2. Include both positive and negative test cases
3. Document expected behavior in `manual_api_tests.md`
4. Update this README with new scenarios

## Performance Notes

- Mock tests: ~100ms (no I/O)
- Integration tests: ~5-10s (includes Docker setup)
- API tests: ~1-2s (depends on server response time)

The endpoint is optimized for high-frequency lookups in EV charging scenarios and should respond within 100ms for typical queries.
