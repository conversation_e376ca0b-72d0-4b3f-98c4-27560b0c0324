package mongo

import (
	"context"
	"errors"

	"go.mongodb.org/mongo-driver/v2/bson"
	"go.mongodb.org/mongo-driver/v2/mongo"

	"inv-cloud-platform/fe-ev-rcpt-tmpl-api/internal/core/domain"
	"inv-cloud-platform/fe-ev-rcpt-tmpl-api/internal/core/port"
)

var _ port.TemplateStoreManager = (*TemplateRepository)(nil)

type TemplateRepository struct {
	collection *mongo.Collection
}

func NewTemplateRepository(collection *mongo.Collection) TemplateRepository {
	return TemplateRepository{
		collection: collection,
	}
}

func (t TemplateRepository) GetAllTemplates(ctx context.Context) (domain.Templates, error) {
	result, err := t.collection.Find(ctx, bson.M{})
	if err != nil {
		return nil, err
	}

	var templates domain.Templates
	err = result.All(ctx, &templates)
	if err != nil {
		return nil, err
	}
	return templates, nil
}

func (t TemplateRepository) GetTemplate(ctx context.Context, connectionID, templateID string) (*domain.Template, error) {
	filter := bson.M{"template_id": templateID, "connection_id": connectionID}
	result := t.collection.FindOne(ctx, filter)
	if err := result.Err(); err != nil {
		return nil, err
	}

	var template *domain.Template
	if err := result.Decode(&template); err != nil {
		return nil, err
	}

	return template, nil
}

func (t TemplateRepository) GetTemplateByCountryAndParty(ctx context.Context, countryCode, partyCode string) (*domain.Template, error) {
	// Use case-insensitive regex for both country code and party code
	filter := bson.M{
		"connection.country_code": bson.M{"$regex": "^" + countryCode + "$", "$options": "i"},
		"connection.party_code":   bson.M{"$regex": "^" + partyCode + "$", "$options": "i"},
	}

	result := t.collection.FindOne(ctx, filter)
	if err := result.Err(); err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, domain.ErrRecordNotFound
		}
		return nil, err
	}

	var template *domain.Template
	if err := result.Decode(&template); err != nil {
		return nil, err
	}

	return template, nil
}

func (t TemplateRepository) SaveTemplate(ctx context.Context, template *domain.Template) error {
	_, err := t.collection.InsertOne(ctx, template)
	return err
}

func (t TemplateRepository) UpdateTemplate(ctx context.Context, template *domain.Template) error {
	update := bson.M{"$set": template}
	filter := bson.M{"template_id": template.TemplateID, "connection_id": template.ConnectionID}

	result, err := t.collection.UpdateOne(ctx, filter, update)
	if err != nil {
		return err
	}
	if result.ModifiedCount == 0 {
		return domain.ErrRecordNotFound
	}
	return nil
}

func (t TemplateRepository) DeleteTemplate(ctx context.Context, connectionID, templateID string) error {
	filter := bson.M{"template_id": templateID, "connection_id": connectionID}
	result, err := t.collection.DeleteOne(ctx, filter)
	if err != nil {
		return err
	}
	if result.DeletedCount == 0 {
		return domain.ErrRecordNotFound
	}
	return nil
}
