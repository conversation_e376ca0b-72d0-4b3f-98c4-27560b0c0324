package domain

import (
	"errors"
	"time"
)

// Template represents a receipt template with all its components
type Template struct {
	TemplateID   string     `json:"template_id" bson:"template_id"`
	ConnectionID string     `json:"connection_id" bson:"connection_id"`
	IsDefault    bool       `json:"is_default" bson:"is_default"`
	Body         string     `json:"body" bson:"body"`
	HeaderText   Text       `json:"header_text" bson:"header_text"`
	HeaderLogo   Logo       `json:"header_logo" bson:"header_logo"`
	FooterText   Text       `json:"footer_text" bson:"footer_text"`
	FooterLogo   Logo       `json:"footer_logo" bson:"footer_logo"`
	Connection   Connection `json:"connection" bson:"connection"`
	LastUpdated  time.Time  `json:"last_updated" bson:"last_updated"`
}

// Templates represents a slice of Template
type Templates []*Template

// Text represents text styling information
type Text struct {
	Font  string `json:"font" bson:"font"`
	Style string `json:"style" bson:"style"`
	Color string `json:"color" bson:"color"`
	Value string `json:"value" bson:"value"`
}

// <PERSON>go represents logo information with dimensions
type Logo struct {
	Src    string `json:"src" bson:"src"`
	Width  int    `json:"width" bson:"width"`
	Height int    `json:"height" bson:"height"`
}

// Connection represents connection information for a template
type Connection struct {
	ConnectionName string `json:"connection_name" bson:"connection_name"`
	CountryCode    string `json:"country_code" bson:"country_code"`
	PartyCode      string `json:"party_code" bson:"party_code"`
	CPOURL         string `json:"cpo_url" bson:"cpo_url"`
}

// Domain errors
var (
	ErrRecordNotFound  = errors.New("record not found")
	ErrInvalidTemplate = errors.New("invalid template")
)
