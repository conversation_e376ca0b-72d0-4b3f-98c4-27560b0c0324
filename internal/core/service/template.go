package service

import (
	"context"
	"time"

	"github.com/google/uuid"

	"inv-cloud-platform/fe-ev-rcpt-tmpl-api/internal/core/domain"
	"inv-cloud-platform/fe-ev-rcpt-tmpl-api/internal/core/port"
)

// TemplateSrv implements the TemplateManager interface
type TemplateSrv struct {
	repo port.TemplateStoreManager
}

// NewTemplateSrv creates a new template service instance
func NewTemplateSrv(repo port.TemplateStoreManager) *TemplateSrv {
	return &TemplateSrv{
		repo: repo,
	}
}

// ListTemplate retrieves all templates
func (s *TemplateSrv) ListTemplate(ctx context.Context) (domain.Templates, error) {
	return s.repo.GetAllTemplates(ctx)
}

// RetrieveTemplate retrieves a specific template by ID and connection ID
func (s *TemplateSrv) RetrieveTemplate(ctx context.Context, templateID, connectionID string) (*domain.Template, error) {
	return s.repo.GetTemplate(ctx, connectionID, templateID)
}

// RetrieveTemplateByCountryAndParty retrieves a template by country code and party code
func (s *TemplateSrv) RetrieveTemplateByCountryAndParty(ctx context.Context, countryCode, partyCode string) (*domain.Template, error) {
	return s.repo.GetTemplateByCountryAndParty(ctx, countryCode, partyCode)
}

// NewTemplate creates a new template
func (s *TemplateSrv) NewTemplate(ctx context.Context, template *domain.Template) error {
	// Validate template
	if err := s.validateTemplate(template); err != nil {
		return err
	}

	// Generate template ID if not provided
	if template.TemplateID == "" {
		template.TemplateID = uuid.New().String()
	}

	// Set last updated timestamp
	template.LastUpdated = time.Now()

	return s.repo.SaveTemplate(ctx, template)
}

// ModifyTemplate updates an existing template
func (s *TemplateSrv) ModifyTemplate(ctx context.Context, template *domain.Template) error {
	// Validate template
	if err := s.validateTemplate(template); err != nil {
		return err
	}

	// Set last updated timestamp
	template.LastUpdated = time.Now()

	return s.repo.UpdateTemplate(ctx, template)
}

// RemoveTemplate deletes a template
func (s *TemplateSrv) RemoveTemplate(ctx context.Context, templateID, connectionID string) error {
	return s.repo.DeleteTemplate(ctx, connectionID, templateID)
}

// validateTemplate performs basic validation on template data
func (s *TemplateSrv) validateTemplate(template *domain.Template) error {
	if template == nil {
		return domain.ErrInvalidTemplate
	}

	if template.ConnectionID == "" {
		return domain.ErrInvalidTemplate
	}

	if template.Body == "" {
		return domain.ErrInvalidTemplate
	}

	return nil
}
