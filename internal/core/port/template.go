package port

import (
	"context"

	"inv-cloud-platform/fe-ev-rcpt-tmpl-api/internal/core/domain"
)

// TemplateManager defines the interface for template business logic operations
type TemplateManager interface {
	ListTemplate(ctx context.Context) (domain.Templates, error)
	RetrieveTemplate(ctx context.Context, templateID, connectionID string) (*domain.Template, error)
	RetrieveTemplateByCountryAndParty(ctx context.Context, countryCode, partyCode string) (*domain.Template, error)
	NewTemplate(ctx context.Context, template *domain.Template) error
	ModifyTemplate(ctx context.Context, template *domain.Template) error
	RemoveTemplate(ctx context.Context, templateID, connectionID string) error
}

// TemplateStoreManager defines the interface for template data persistence operations
type TemplateStoreManager interface {
	GetAllTemplates(ctx context.Context) (domain.Templates, error)
	GetTemplate(ctx context.Context, connectionID, templateID string) (*domain.Template, error)
	GetTemplateByCountryAndParty(ctx context.Context, countryCode, partyCode string) (*domain.Template, error)
	SaveTemplate(ctx context.Context, template *domain.Template) error
	UpdateTemplate(ctx context.Context, template *domain.Template) error
	DeleteTemplate(ctx context.Context, connectionID, templateID string) error
}
