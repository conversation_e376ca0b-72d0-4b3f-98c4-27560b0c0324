package main

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http/httptest"
	"time"

	"github.com/google/uuid"

	"inv-cloud-platform/fe-ev-rcpt-tmpl-api/internal/adapter/resthttp"
	"inv-cloud-platform/fe-ev-rcpt-tmpl-api/internal/core/domain"
	"inv-cloud-platform/fe-ev-rcpt-tmpl-api/internal/mock"
	"inv-cloud-platform/fe-ev-rcpt-tmpl-api/openapi"
)

func main() {
	fmt.Println("=== Quick Manual Test: Get Template by Country Code and Party Code ===")
	fmt.Println("🚀 Testing the RetrieveTemplateByCountryAndParty endpoint with mock data")
	fmt.Println()

	// Create mock service
	mockService := &mock.TemplateServiceMock{}
	handler := resthttp.NewHandler(mockService)

	// Create sample template data
	sampleTemplate := &domain.Template{
		TemplateID:   uuid.New().String(),
		ConnectionID: uuid.New().String(),
		IsDefault:    false,
		Body:         "Sample EV Charging Receipt",
		HeaderText: domain.Text{
			Font:  "Arial",
			Style: "bold",
			Color: "#000000",
			Value: "🇺🇸 US EV Charging Station",
		},
		HeaderLogo: domain.Logo{
			Src:    "https://example.com/logo.png",
			Width:  200,
			Height: 100,
		},
		FooterText: domain.Text{
			Font:  "Arial",
			Style: "normal",
			Color: "#666666",
			Value: "Thank you for choosing our service!",
		},
		FooterLogo: domain.Logo{
			Src:    "https://example.com/footer-logo.png",
			Width:  150,
			Height: 50,
		},
		Connection: domain.Connection{
			ConnectionName: "US Charging Network",
			CountryCode:    "US",
			PartyCode:      "ABC123",
			CPOURL:         "https://us-charging.example.com",
		},
		LastUpdated: time.Now(),
	}

	// Define test cases
	testCases := []struct {
		name        string
		countryCode string
		partyCode   string
		setupMock   func()
		expectCode  int
		description string
	}{
		{
			name:        "Successful Retrieval - US/ABC123",
			countryCode: "US",
			partyCode:   "ABC123",
			setupMock: func() {
				mockService.On("RetrieveTemplateByCountryAndParty", context.Background(), "US", "ABC123").
					Return(sampleTemplate, nil).Once()
			},
			expectCode:  200,
			description: "Should successfully return template for US/ABC123",
		},
		{
			name:        "Successful Retrieval - Case Insensitive",
			countryCode: "us",
			partyCode:   "abc123",
			setupMock: func() {
				mockService.On("RetrieveTemplateByCountryAndParty", context.Background(), "us", "abc123").
					Return(sampleTemplate, nil).Once()
			},
			expectCode:  200,
			description: "Should successfully return template with lowercase input",
		},
		{
			name:        "Template Not Found",
			countryCode: "XX",
			partyCode:   "NOTFOUND",
			setupMock: func() {
				mockService.On("RetrieveTemplateByCountryAndParty", context.Background(), "XX", "NOTFOUND").
					Return(nil, domain.ErrRecordNotFound).Once()
			},
			expectCode:  404,
			description: "Should return 404 when template is not found",
		},
		{
			name:        "Invalid Country Code",
			countryCode: "X",
			partyCode:   "ABC123",
			setupMock:   func() {}, // No mock setup needed for validation errors
			expectCode:  400,
			description: "Should return 400 for invalid country code (too short)",
		},
		{
			name:        "Empty Party Code",
			countryCode: "US",
			partyCode:   "",
			setupMock:   func() {}, // No mock setup needed for validation errors
			expectCode:  400,
			description: "Should return 400 for empty party code",
		},
		{
			name:        "Service Error",
			countryCode: "US",
			partyCode:   "ERROR",
			setupMock: func() {
				mockService.On("RetrieveTemplateByCountryAndParty", context.Background(), "US", "ERROR").
					Return(nil, fmt.Errorf("internal service error")).Once()
			},
			expectCode:  500,
			description: "Should return 500 for internal service errors",
		},
	}

	// Run tests
	fmt.Println("🧪 Running Test Cases")
	fmt.Println("=" * 60)

	passCount := 0
	totalTests := len(testCases)

	for i, test := range testCases {
		fmt.Printf("\n📋 Test %d: %s\n", i+1, test.name)
		fmt.Printf("   📝 %s\n", test.description)
		fmt.Printf("   🔗 GET /api/v1/templates/%s/%s\n", test.countryCode, test.partyCode)

		// Setup mock expectations
		test.setupMock()

		// Create HTTP request
		req := httptest.NewRequest("GET", 
			fmt.Sprintf("/api/v1/templates/%s/%s", test.countryCode, test.partyCode), 
			nil)
		rr := httptest.NewRecorder()

		// Call the handler
		handler.GetTemplateByCountryAndParty(rr, req, test.countryCode, test.partyCode)

		// Check response status
		fmt.Printf("   📊 Response: %d %s\n", rr.Code, getStatusText(rr.Code))

		if rr.Code == test.expectCode {
			fmt.Printf("   ✅ PASS - Status code matches expected %d\n", test.expectCode)
			passCount++
		} else {
			fmt.Printf("   ❌ FAIL - Expected %d, got %d\n", test.expectCode, rr.Code)
		}

		// Display response details
		if rr.Code == 200 {
			var response openapi.TemplateResponse
			if err := json.Unmarshal(rr.Body.Bytes(), &response); err == nil {
				fmt.Printf("   📄 Template Details:\n")
				fmt.Printf("      - Template ID: %s\n", response.TemplateId)
				fmt.Printf("      - Connection: %s\n", response.Connection.ConnectionName)
				fmt.Printf("      - Country: %s, Party: %s\n", 
					response.Connection.CountryCode, response.Connection.PartyCode)
				fmt.Printf("      - Body Preview: %.50s...\n", response.Body)
			} else {
				fmt.Printf("   ⚠️  Could not parse response JSON: %v\n", err)
			}
		} else if rr.Code >= 400 {
			fmt.Printf("   📄 Error Response: %s\n", rr.Body.String())
		}

		// Verify mock expectations
		if test.expectCode != 400 { // Skip mock verification for validation errors
			mockService.AssertExpectations(nil)
		}
	}

	// Test Summary
	fmt.Println("\n\n🎯 Test Summary")
	fmt.Println("=" * 30)
	fmt.Printf("✅ Passed: %d/%d tests\n", passCount, totalTests)
	if passCount == totalTests {
		fmt.Println("🎉 All tests passed!")
	} else {
		fmt.Printf("❌ Failed: %d/%d tests\n", totalTests-passCount, totalTests)
	}

	// Feature verification
	fmt.Println("\n🔧 Features Tested:")
	fmt.Println("   ✓ Successful template retrieval")
	fmt.Println("   ✓ Case-insensitive matching")
	fmt.Println("   ✓ Template not found handling")
	fmt.Println("   ✓ Input validation (country code length)")
	fmt.Println("   ✓ Input validation (empty party code)")
	fmt.Println("   ✓ Service error handling")

	fmt.Println("\n📋 API Endpoint Specification:")
	fmt.Println("   Path: GET /api/v1/templates/{country_code}/{party_code}")
	fmt.Println("   Parameters:")
	fmt.Println("     - country_code: ISO 3166-1 alpha-2 country code (min 2 chars)")
	fmt.Println("     - party_code: Party identifier (min 1 char)")
	fmt.Println("   Responses:")
	fmt.Println("     - 200: Template found and returned")
	fmt.Println("     - 400: Invalid input parameters")
	fmt.Println("     - 404: Template not found")
	fmt.Println("     - 500: Internal server error")

	fmt.Println("\n🚀 Manual Test Complete!")
}

func getStatusText(code int) string {
	switch code {
	case 200:
		return "OK"
	case 400:
		return "Bad Request"
	case 404:
		return "Not Found"
	case 500:
		return "Internal Server Error"
	default:
		return "Unknown"
	}
}

// Helper function to repeat strings (Go doesn't have built-in string multiplication)
func repeat(s string, count int) string {
	result := ""
	for i := 0; i < count; i++ {
		result += s
	}
	return result
}
