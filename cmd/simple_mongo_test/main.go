package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http/httptest"
	"os"
	"time"

	"github.com/google/uuid"
	"go.mongodb.org/mongo-driver/v2/mongo"
	"go.mongodb.org/mongo-driver/v2/mongo/options"

	mongoadapter "inv-cloud-platform/fe-ev-rcpt-tmpl-api/internal/adapter/mongo"
	"inv-cloud-platform/fe-ev-rcpt-tmpl-api/internal/adapter/resthttp"
	"inv-cloud-platform/fe-ev-rcpt-tmpl-api/internal/core/domain"
	"inv-cloud-platform/fe-ev-rcpt-tmpl-api/internal/core/service"
	"inv-cloud-platform/fe-ev-rcpt-tmpl-api/openapi"
)

const templateCollection = "templates"

func main() {
	fmt.Println("🧪 MANUAL MONGODB TEST: GET Template API by Country Code and Party Code")
	fmt.Println("=====================================================================")
	fmt.Println()

	// Get MongoDB connection string from environment or use default
	mongoURI := os.Getenv("MONGO_URI")
	if mongoURI == "" {
		mongoURI = "mongodb://localhost:27017"
	}

	fmt.Printf("🔗 Connecting to MongoDB: %s\n", mongoURI)

	ctx := context.Background()

	// Connect to MongoDB
	client, err := mongo.Connect(options.Client().ApplyURI(mongoURI))
	if err != nil {
		log.Fatalf("❌ Failed to connect to MongoDB: %v\nPlease ensure MongoDB is running on localhost:27017 or set MONGO_URI environment variable", err)
	}
	defer client.Disconnect(ctx)

	// Test connection
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	
	if err := client.Ping(ctx, nil); err != nil {
		log.Fatalf("❌ Failed to ping MongoDB: %v\nPlease ensure MongoDB is running and accessible", err)
	}

	fmt.Println("✅ Connected to MongoDB successfully")

	// Setup database and collection
	db := client.Database("test_ev_templates")
	collection := db.Collection(templateCollection)

	// Clear existing test data
	fmt.Println("🧹 Clearing existing test data...")
	collection.Drop(context.Background())

	// Create repository and service
	templateRepo := mongoadapter.NewTemplateRepository(collection)
	templateSrv := service.NewTemplateSrv(templateRepo)
	handler := resthttp.NewHandler(templateSrv)

	fmt.Println("✅ Services initialized")
	fmt.Println()

	// Create test data
	fmt.Println("📝 Creating test data...")
	testTemplates := createTestTemplates()
	
	for key, template := range testTemplates {
		if err := templateRepo.SaveTemplate(context.Background(), template); err != nil {
			log.Fatalf("Failed to save template %s: %v", key, err)
		}
		fmt.Printf("   ✅ Saved: %s (%s/%s)\n", 
			template.Connection.ConnectionName,
			template.Connection.CountryCode, 
			template.Connection.PartyCode)
	}

	fmt.Println()

	// Run test scenarios
	fmt.Println("🔍 Running HTTP Handler Test Scenarios...")
	fmt.Println()

	testScenarios := []struct {
		name        string
		countryCode string
		partyCode   string
		expectCode  int
		description string
	}{
		{
			name:        "Exact Case Match",
			countryCode: "US",
			partyCode:   "ABC123",
			expectCode:  200,
			description: "Should find US/ABC123 template with exact case",
		},
		{
			name:        "Case Insensitive - Lowercase",
			countryCode: "us",
			partyCode:   "abc123",
			expectCode:  200,
			description: "Should find US/ABC123 template with lowercase input",
		},
		{
			name:        "Case Insensitive - Mixed Case",
			countryCode: "Us",
			partyCode:   "AbC123",
			expectCode:  200,
			description: "Should find US/ABC123 template with mixed case input",
		},
		{
			name:        "Different Template - DE",
			countryCode: "DE",
			partyCode:   "XYZ789",
			expectCode:  200,
			description: "Should find DE/XYZ789 template",
		},
		{
			name:        "Case Insensitive - DE lowercase",
			countryCode: "de",
			partyCode:   "xyz789",
			expectCode:  200,
			description: "Should find DE/XYZ789 template with lowercase input",
		},
		{
			name:        "Template Not Found",
			countryCode: "XX",
			partyCode:   "NOTFOUND",
			expectCode:  404,
			description: "Should return 404 for non-existent template",
		},
		{
			name:        "Invalid Country Code",
			countryCode: "X",
			partyCode:   "ABC123",
			expectCode:  400,
			description: "Should return 400 for invalid country code",
		},
		{
			name:        "Empty Party Code",
			countryCode: "US",
			partyCode:   "",
			expectCode:  400,
			description: "Should return 400 for empty party code",
		},
	}

	passCount := 0
	totalTests := len(testScenarios)

	for i, scenario := range testScenarios {
		fmt.Printf("Test %d: %s\n", i+1, scenario.name)
		fmt.Printf("   📝 %s\n", scenario.description)
		fmt.Printf("   🌐 GET /api/v1/templates/%s/%s\n", scenario.countryCode, scenario.partyCode)

		// Create HTTP request
		url := fmt.Sprintf("/api/v1/templates/%s/%s", scenario.countryCode, scenario.partyCode)
		req := httptest.NewRequest("GET", url, nil)
		req = req.WithContext(context.Background())

		// Create response recorder
		rr := httptest.NewRecorder()

		// Call handler
		handler.GetTemplateByCountryAndParty(rr, req, scenario.countryCode, scenario.partyCode)

		// Check response
		fmt.Printf("   📤 Response Code: %d (expected: %d)\n", rr.Code, scenario.expectCode)

		if rr.Code == scenario.expectCode {
			fmt.Printf("   ✅ PASS\n")
			passCount++

			// Show response details for successful cases
			if rr.Code == 200 {
				var response openapi.TemplateResponse
				if err := json.Unmarshal(rr.Body.Bytes(), &response); err == nil {
					fmt.Printf("   📋 Found Template:\n")
					fmt.Printf("      - Connection: %s\n", response.Connection.ConnectionName)
					fmt.Printf("      - Country: %s\n", response.Connection.CountryCode)
					fmt.Printf("      - Party: %s\n", response.Connection.PartyCode)
					fmt.Printf("      - Header: %s\n", response.HeaderText.Value)
				}
			}
		} else {
			fmt.Printf("   ❌ FAIL\n")
			if rr.Code >= 400 {
				fmt.Printf("   📤 Error: %s\n", rr.Body.String())
			}
		}

		fmt.Println()
	}

	// Test direct repository calls
	fmt.Println("🔧 Testing Repository Layer Directly...")
	fmt.Println()

	directTests := []struct {
		name        string
		countryCode string
		partyCode   string
		shouldFind  bool
	}{
		{"Direct - Exact Case", "US", "ABC123", true},
		{"Direct - Lowercase", "us", "abc123", true},
		{"Direct - Mixed Case", "De", "XyZ789", true},
		{"Direct - Uppercase", "FR", "DEF456", true},
		{"Direct - All Lowercase", "fr", "def456", true},
		{"Direct - Not Found", "XX", "NOTFOUND", false},
	}

	for i, test := range directTests {
		fmt.Printf("Direct Test %d: %s\n", i+1, test.name)
		fmt.Printf("   🔍 Repository.GetTemplateByCountryAndParty('%s', '%s')\n", test.countryCode, test.partyCode)

		template, err := templateRepo.GetTemplateByCountryAndParty(context.Background(), test.countryCode, test.partyCode)

		if test.shouldFind {
			if err == nil && template != nil {
				fmt.Printf("   ✅ PASS - Found: %s (%s/%s)\n", 
					template.Connection.ConnectionName,
					template.Connection.CountryCode,
					template.Connection.PartyCode)
			} else {
				fmt.Printf("   ❌ FAIL - Expected to find template but got error: %v\n", err)
			}
		} else {
			if err != nil && template == nil {
				fmt.Printf("   ✅ PASS - Correctly returned not found\n")
			} else {
				fmt.Printf("   ❌ FAIL - Expected not found but got template: %v\n", template)
			}
		}
		fmt.Println()
	}

	// Summary
	fmt.Println("📊 TEST SUMMARY")
	fmt.Println("===============")
	fmt.Printf("✅ HTTP Handler Tests Passed: %d/%d\n", passCount, totalTests)
	fmt.Printf("🐳 MongoDB Connection: %s\n", mongoURI)
	fmt.Printf("📁 Database: test_ev_templates\n")
	fmt.Printf("📄 Collection: %s\n", templateCollection)
	fmt.Println()

	if passCount == totalTests {
		fmt.Println("🎉 ALL TESTS PASSED!")
		fmt.Println("✅ Case insensitive matching works correctly with real MongoDB")
		fmt.Println("✅ HTTP handlers work correctly")
		fmt.Println("✅ Repository layer works correctly")
		fmt.Println("✅ Input validation works as expected")
	} else {
		fmt.Printf("⚠️  %d tests failed\n", totalTests-passCount)
	}

	fmt.Println()
	fmt.Println("🔧 MongoDB Query Verification")
	fmt.Println("============================")
	fmt.Println("The implementation uses MongoDB regex queries:")
	fmt.Printf("db.%s.findOne({\n", templateCollection)
	fmt.Println(`  "connection.country_code": { "$regex": "^us$", "$options": "i" },`)
	fmt.Println(`  "connection.party_code": { "$regex": "^abc123$", "$options": "i" }`)
	fmt.Println(`})`)
	fmt.Println()
	fmt.Println("✅ Verified case-insensitive matching:")
	fmt.Println("  - US/ABC123 matches us/abc123")
	fmt.Println("  - DE/XYZ789 matches de/xyz789") 
	fmt.Println("  - FR/DEF456 matches fr/def456")
}

func createTestTemplates() map[string]*domain.Template {
	templates := make(map[string]*domain.Template)

	// US Template
	templates["US-ABC123"] = &domain.Template{
		TemplateID:   uuid.New().String(),
		ConnectionID: uuid.New().String(),
		IsDefault:    false,
		Body:         "US EV Charging Receipt",
		HeaderText: domain.Text{
			Font:  "Arial",
			Style: "bold",
			Color: "#000000",
			Value: "🇺🇸 US EV Charging Station",
		},
		HeaderLogo: domain.Logo{
			Src:    "https://us-charging.example.com/logo.png",
			Width:  200,
			Height: 100,
		},
		FooterText: domain.Text{
			Font:  "Arial",
			Style: "normal",
			Color: "#666666",
			Value: "Thank you for charging with us!",
		},
		Connection: domain.Connection{
			ConnectionName: "US Charging Network",
			CountryCode:    "US",
			PartyCode:      "ABC123",
			CPOURL:         "https://us-charging.example.com",
		},
		LastUpdated: time.Now(),
	}

	// German Template
	templates["DE-XYZ789"] = &domain.Template{
		TemplateID:   uuid.New().String(),
		ConnectionID: uuid.New().String(),
		IsDefault:    false,
		Body:         "German EV Charging Receipt",
		HeaderText: domain.Text{
			Font:  "Arial",
			Style: "bold",
			Color: "#000000",
			Value: "🇩🇪 Deutsche Ladestation",
		},
		Connection: domain.Connection{
			ConnectionName: "German Charging Network",
			CountryCode:    "DE",
			PartyCode:      "XYZ789",
			CPOURL:         "https://de-charging.example.com",
		},
		LastUpdated: time.Now(),
	}

	// French Template
	templates["FR-DEF456"] = &domain.Template{
		TemplateID:   uuid.New().String(),
		ConnectionID: uuid.New().String(),
		IsDefault:    false,
		Body:         "French EV Charging Receipt",
		HeaderText: domain.Text{
			Font:  "Arial",
			Style: "bold",
			Color: "#000000",
			Value: "🇫🇷 Station de Recharge Française",
		},
		Connection: domain.Connection{
			ConnectionName: "French Charging Network",
			CountryCode:    "FR",
			PartyCode:      "DEF456",
			CPOURL:         "https://fr-charging.example.com",
		},
		LastUpdated: time.Now(),
	}

	return templates
}
