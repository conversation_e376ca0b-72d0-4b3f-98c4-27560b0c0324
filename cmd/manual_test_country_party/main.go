package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http/httptest"
	"time"

	"github.com/google/uuid"
	"github.com/testcontainers/testcontainers-go/modules/mongodb"
	"go.mongodb.org/mongo-driver/v2/mongo"
	"go.mongodb.org/mongo-driver/v2/mongo/options"

	mongoadapter "inv-cloud-platform/fe-ev-rcpt-tmpl-api/internal/adapter/mongo"
	"inv-cloud-platform/fe-ev-rcpt-tmpl-api/internal/adapter/resthttp"
	"inv-cloud-platform/fe-ev-rcpt-tmpl-api/internal/core/domain"
	"inv-cloud-platform/fe-ev-rcpt-tmpl-api/internal/core/service"
	"inv-cloud-platform/fe-ev-rcpt-tmpl-api/openapi"
)

const templateCollection = "templates"

func main() {
	fmt.Println("=== Manual Test: Get Template by Country Code and Party Code ===")
	fmt.Println()

	ctx := context.Background()

	// Start MongoDB container
	mongoContainer, err := mongodb.Run(ctx, "mongo:7")
	if err != nil {
		log.Fatalf("Failed to start MongoDB container: %v", err)
	}
	defer func() {
		if err := mongoContainer.Terminate(ctx); err != nil {
			log.Printf("Failed to terminate MongoDB container: %v", err)
		}
	}()

	// Get connection string
	connectionString, err := mongoContainer.ConnectionString(ctx)
	if err != nil {
		log.Fatalf("Failed to get connection string: %v", err)
	}

	// Connect to MongoDB
	client, err := mongo.Connect(options.Client().ApplyURI(connectionString))
	if err != nil {
		log.Fatalf("Failed to connect to MongoDB: %v", err)
	}
	defer client.Disconnect(ctx)

	// Test connection
	if err := client.Ping(ctx, nil); err != nil {
		log.Fatalf("Failed to ping MongoDB: %v", err)
	}

	// Setup database and collection
	db := client.Database("test_db")
	collection := db.Collection(templateCollection)

	// Create repository and service
	templateRepo := mongoadapter.NewTemplateRepository(collection)
	templateSrv := service.NewTemplateSrv(templateRepo)
	handler := resthttp.NewHandler(templateSrv)

	// Insert test templates
	testTemplates := createTestTemplates()
	fmt.Printf("📝 Inserting %d test templates...\n", len(testTemplates))
	for key, template := range testTemplates {
		if err := templateRepo.SaveTemplate(ctx, template); err != nil {
			log.Printf("Failed to save template %s: %v", key, err)
		} else {
			fmt.Printf("   ✅ Saved template: %s (%s/%s)\n", 
				template.Connection.ConnectionName, 
				template.Connection.CountryCode, 
				template.Connection.PartyCode)
		}
	}
	fmt.Println()

	// Define test cases
	testCases := []struct {
		name        string
		countryCode string
		partyCode   string
		expectCode  int
		description string
	}{
		{
			name:        "US Template - Exact Match",
			countryCode: "US",
			partyCode:   "ABC123",
			expectCode:  200,
			description: "Should find US/ABC123 template",
		},
		{
			name:        "US Template - Case Insensitive",
			countryCode: "us",
			partyCode:   "abc123",
			expectCode:  200,
			description: "Should find US/ABC123 template with lowercase input",
		},
		{
			name:        "German Template - Exact Match",
			countryCode: "DE",
			partyCode:   "XYZ789",
			expectCode:  200,
			description: "Should find DE/XYZ789 template",
		},
		{
			name:        "German Template - Mixed Case",
			countryCode: "De",
			partyCode:   "xyz789",
			expectCode:  200,
			description: "Should find DE/XYZ789 template with mixed case",
		},
		{
			name:        "French Template - Exact Match",
			countryCode: "FR",
			partyCode:   "DEF456",
			expectCode:  200,
			description: "Should find FR/DEF456 template",
		},
		{
			name:        "Template Not Found",
			countryCode: "XX",
			partyCode:   "NOTFOUND",
			expectCode:  404,
			description: "Should return 404 for non-existent template",
		},
		{
			name:        "Invalid Country Code - Too Short",
			countryCode: "X",
			partyCode:   "ABC123",
			expectCode:  400,
			description: "Should return 400 for invalid country code",
		},
		{
			name:        "Invalid Party Code - Empty",
			countryCode: "US",
			partyCode:   "",
			expectCode:  400,
			description: "Should return 400 for empty party code",
		},
	}

	// Run HTTP endpoint tests
	fmt.Println("🌐 Testing HTTP Endpoint: GET /api/v1/templates/{country_code}/{party_code}")
	fmt.Println("=" * 80)

	for i, test := range testCases {
		fmt.Printf("\nTest %d: %s\n", i+1, test.name)
		fmt.Printf("   📋 %s\n", test.description)
		fmt.Printf("   🔗 GET /api/v1/templates/%s/%s\n", test.countryCode, test.partyCode)

		// Create HTTP request
		req := httptest.NewRequest("GET", 
			fmt.Sprintf("/api/v1/templates/%s/%s", test.countryCode, test.partyCode), 
			nil)
		rr := httptest.NewRecorder()

		// Call handler
		handler.GetTemplateByCountryAndParty(rr, req, test.countryCode, test.partyCode)

		// Check response
		fmt.Printf("   📊 Response: %d %s\n", rr.Code, getStatusText(rr.Code))

		if rr.Code == test.expectCode {
			fmt.Printf("   ✅ PASS - Expected status code %d\n", test.expectCode)
		} else {
			fmt.Printf("   ❌ FAIL - Expected %d, got %d\n", test.expectCode, rr.Code)
		}

		// Parse and display response body for successful requests
		if rr.Code == 200 {
			var response openapi.TemplateResponse
			if err := json.Unmarshal(rr.Body.Bytes(), &response); err == nil {
				fmt.Printf("   📄 Template Found:\n")
				fmt.Printf("      - ID: %s\n", response.TemplateId)
				fmt.Printf("      - Connection: %s\n", response.Connection.ConnectionName)
				fmt.Printf("      - Country: %s\n", response.Connection.CountryCode)
				fmt.Printf("      - Party: %s\n", response.Connection.PartyCode)
				fmt.Printf("      - Default: %t\n", response.IsDefault)
			}
		} else if rr.Code >= 400 {
			fmt.Printf("   📄 Error Response: %s\n", rr.Body.String())
		}
	}

	// Test direct repository calls
	fmt.Println("\n\n🔍 Testing Direct Repository Calls")
	fmt.Println("=" * 50)

	directTests := []struct {
		name        string
		countryCode string
		partyCode   string
		shouldFind  bool
	}{
		{"US Template Direct", "US", "ABC123", true},
		{"US Template Case Insensitive", "us", "abc123", true},
		{"German Template Direct", "DE", "XYZ789", true},
		{"French Template Direct", "FR", "DEF456", true},
		{"Non-existent Template", "XX", "NOTFOUND", false},
	}

	for i, test := range directTests {
		fmt.Printf("\nDirect Test %d: %s\n", i+1, test.name)
		fmt.Printf("   🔍 Repository.GetTemplateByCountryAndParty('%s', '%s')\n", test.countryCode, test.partyCode)

		template, err := templateRepo.GetTemplateByCountryAndParty(ctx, test.countryCode, test.partyCode)

		if test.shouldFind {
			if err == nil && template != nil {
				fmt.Printf("   ✅ PASS - Found template: %s\n", template.Connection.ConnectionName)
				fmt.Printf("      - Template ID: %s\n", template.TemplateID)
				fmt.Printf("      - Connection ID: %s\n", template.ConnectionID)
			} else {
				fmt.Printf("   ❌ FAIL - Expected to find template but got error: %v\n", err)
			}
		} else {
			if err != nil && template == nil {
				fmt.Printf("   ✅ PASS - Correctly returned not found\n")
			} else {
				fmt.Printf("   ❌ FAIL - Expected not found but got template: %v\n", template)
			}
		}
	}

	// Test service layer calls
	fmt.Println("\n\n⚙️  Testing Service Layer Calls")
	fmt.Println("=" * 40)

	serviceTests := []struct {
		name        string
		countryCode string
		partyCode   string
		shouldFind  bool
	}{
		{"Service US Template", "US", "ABC123", true},
		{"Service Case Insensitive", "us", "abc123", true},
		{"Service German Template", "DE", "XYZ789", true},
		{"Service Not Found", "XX", "NOTFOUND", false},
	}

	for i, test := range serviceTests {
		fmt.Printf("\nService Test %d: %s\n", i+1, test.name)
		fmt.Printf("   ⚙️  Service.RetrieveTemplateByCountryAndParty('%s', '%s')\n", test.countryCode, test.partyCode)

		template, err := templateSrv.RetrieveTemplateByCountryAndParty(ctx, test.countryCode, test.partyCode)

		if test.shouldFind {
			if err == nil && template != nil {
				fmt.Printf("   ✅ PASS - Found template: %s\n", template.Connection.ConnectionName)
			} else {
				fmt.Printf("   ❌ FAIL - Expected to find template but got error: %v\n", err)
			}
		} else {
			if err != nil && template == nil {
				fmt.Printf("   ✅ PASS - Correctly returned not found\n")
			} else {
				fmt.Printf("   ❌ FAIL - Expected not found but got template: %v\n", template)
			}
		}
	}

	fmt.Println("\n\n🎯 Test Summary")
	fmt.Println("=" * 20)
	fmt.Println("✅ All layers tested:")
	fmt.Println("   - HTTP Handler Layer")
	fmt.Println("   - Service Layer") 
	fmt.Println("   - Repository Layer")
	fmt.Println()
	fmt.Println("🔧 Features Verified:")
	fmt.Println("   - Case-insensitive country/party code matching")
	fmt.Println("   - Proper error handling for not found")
	fmt.Println("   - Input validation for invalid parameters")
	fmt.Println("   - End-to-end request/response flow")
}

func createTestTemplates() map[string]*domain.Template {
	templates := make(map[string]*domain.Template)

	// US Template
	templates["US-ABC123"] = &domain.Template{
		TemplateID:   uuid.New().String(),
		ConnectionID: uuid.New().String(),
		IsDefault:    false,
		Body:         "US EV Charging Receipt Template",
		HeaderText: domain.Text{
			Font:  "Arial",
			Style: "bold",
			Color: "#000000",
			Value: "🇺🇸 US EV Charging Station",
		},
		HeaderLogo: domain.Logo{
			Src:    "https://us-charging.example.com/logo.png",
			Width:  200,
			Height: 100,
		},
		FooterText: domain.Text{
			Font:  "Arial",
			Style: "normal",
			Color: "#666666",
			Value: "Thank you for charging with us!",
		},
		Connection: domain.Connection{
			ConnectionName: "US Charging Network",
			CountryCode:    "US",
			PartyCode:      "ABC123",
			CPOURL:         "https://us-charging.example.com",
		},
		LastUpdated: time.Now(),
	}

	// German Template
	templates["DE-XYZ789"] = &domain.Template{
		TemplateID:   uuid.New().String(),
		ConnectionID: uuid.New().String(),
		IsDefault:    false,
		Body:         "German EV Charging Receipt Template",
		HeaderText: domain.Text{
			Font:  "Arial",
			Style: "bold",
			Color: "#000000",
			Value: "🇩🇪 Deutsche Ladestation",
		},
		HeaderLogo: domain.Logo{
			Src:    "https://de-charging.example.com/logo.png",
			Width:  200,
			Height: 100,
		},
		FooterText: domain.Text{
			Font:  "Arial",
			Style: "normal",
			Color: "#666666",
			Value: "Vielen Dank fürs Laden!",
		},
		Connection: domain.Connection{
			ConnectionName: "German Charging Network",
			CountryCode:    "DE",
			PartyCode:      "XYZ789",
			CPOURL:         "https://de-charging.example.com",
		},
		LastUpdated: time.Now(),
	}

	// French Template
	templates["FR-DEF456"] = &domain.Template{
		TemplateID:   uuid.New().String(),
		ConnectionID: uuid.New().String(),
		IsDefault:    false,
		Body:         "French EV Charging Receipt Template",
		HeaderText: domain.Text{
			Font:  "Arial",
			Style: "bold",
			Color: "#000000",
			Value: "🇫🇷 Station de Recharge Française",
		},
		HeaderLogo: domain.Logo{
			Src:    "https://fr-charging.example.com/logo.png",
			Width:  200,
			Height: 100,
		},
		FooterText: domain.Text{
			Font:  "Arial",
			Style: "normal",
			Color: "#666666",
			Value: "Merci de nous avoir choisis!",
		},
		Connection: domain.Connection{
			ConnectionName: "French Charging Network",
			CountryCode:    "FR",
			PartyCode:      "DEF456",
			CPOURL:         "https://fr-charging.example.com",
		},
		LastUpdated: time.Now(),
	}

	return templates
}

func getStatusText(code int) string {
	switch code {
	case 200:
		return "OK"
	case 400:
		return "Bad Request"
	case 404:
		return "Not Found"
	case 500:
		return "Internal Server Error"
	default:
		return "Unknown"
	}
}
