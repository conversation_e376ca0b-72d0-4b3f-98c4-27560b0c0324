#!/bin/bash

# Manual Test Script for Get Template by Country and Party Code Endpoint
# Usage: ./run_manual_tests.sh [base_url]
# Default base_url: http://localhost:8080

set -e

# Configuration
BASE_URL=${1:-"http://localhost:8080"}
ENDPOINT="/api/v1/templates"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Counters
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

echo -e "${BLUE}=== Manual API Tests for Get Template by Country and Party Code ===${NC}"
echo -e "${BLUE}Base URL: ${BASE_URL}${NC}"
echo -e "${BLUE}Endpoint: GET ${ENDPOINT}/{country_code}/{party_code}${NC}"
echo ""

# Function to run a test
run_test() {
    local test_name="$1"
    local country_code="$2"
    local party_code="$3"
    local expected_status="$4"
    local description="$5"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    echo -e "${YELLOW}Test ${TOTAL_TESTS}: ${test_name}${NC}"
    echo -e "  📝 ${description}"
    echo -e "  🔗 GET ${ENDPOINT}/${country_code}/${party_code}"
    
    # Make the request
    local url="${BASE_URL}${ENDPOINT}/${country_code}/${party_code}"
    local response=$(curl -s -w "\n%{http_code}" "$url" -H "Accept: application/json")
    local body=$(echo "$response" | head -n -1)
    local status=$(echo "$response" | tail -n 1)
    
    echo -e "  📊 Response: ${status}"
    
    # Check if status matches expected
    if [ "$status" = "$expected_status" ]; then
        echo -e "  ${GREEN}✅ PASS - Status code matches expected ${expected_status}${NC}"
        PASSED_TESTS=$((PASSED_TESTS + 1))
        
        # Show response body for successful requests
        if [ "$status" = "200" ]; then
            echo -e "  📄 Response Body:"
            echo "$body" | jq '.' 2>/dev/null || echo "$body"
        fi
    else
        echo -e "  ${RED}❌ FAIL - Expected ${expected_status}, got ${status}${NC}"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        echo -e "  📄 Response Body: ${body}"
    fi
    
    echo ""
}

# Function to check if server is running
check_server() {
    echo -e "${BLUE}🔍 Checking if server is running...${NC}"
    if curl -s "${BASE_URL}/health" > /dev/null 2>&1; then
        echo -e "${GREEN}✅ Server is running${NC}"
    else
        echo -e "${RED}❌ Server is not responding at ${BASE_URL}${NC}"
        echo -e "${YELLOW}Please start the server with: go run main.go${NC}"
        exit 1
    fi
    echo ""
}

# Check if curl and jq are available
check_dependencies() {
    if ! command -v curl &> /dev/null; then
        echo -e "${RED}❌ curl is required but not installed${NC}"
        exit 1
    fi
    
    if ! command -v jq &> /dev/null; then
        echo -e "${YELLOW}⚠️  jq is not installed - JSON responses will not be formatted${NC}"
    fi
}

# Main test execution
main() {
    check_dependencies
    check_server
    
    echo -e "${BLUE}🧪 Running Test Cases${NC}"
    echo "=================================================================="
    
    # Test 1: Successful retrieval - US template
    run_test "US Template Success" "US" "ABC123" "200" "Should successfully return US template"
    
    # Test 2: Case insensitive matching
    run_test "Case Insensitive" "us" "abc123" "200" "Should return template with lowercase input"
    
    # Test 3: German template
    run_test "German Template" "DE" "XYZ789" "200" "Should successfully return German template"
    
    # Test 4: Mixed case
    run_test "Mixed Case" "De" "xyz789" "200" "Should return template with mixed case input"
    
    # Test 5: French template
    run_test "French Template" "FR" "DEF456" "200" "Should successfully return French template"
    
    # Test 6: Template not found
    run_test "Not Found" "XX" "NOTFOUND" "404" "Should return 404 for non-existent template"
    
    # Test 7: Invalid country code
    run_test "Invalid Country Code" "X" "ABC123" "400" "Should return 400 for invalid country code"
    
    # Test 8: Empty party code (this might be handled by routing)
    run_test "Empty Party Code" "US" "" "400" "Should return 400 for empty party code"
    
    # Test 9: Special characters
    run_test "Special Characters" "US" "ABC-123" "404" "Should handle special characters in party code"
    
    # Test 10: Numeric country code
    run_test "Numeric Country" "12" "ABC123" "404" "Should return 404 for numeric country code"
    
    # Test Summary
    echo -e "${BLUE}🎯 Test Summary${NC}"
    echo "=============================="
    echo -e "Total Tests: ${TOTAL_TESTS}"
    echo -e "${GREEN}Passed: ${PASSED_TESTS}${NC}"
    echo -e "${RED}Failed: ${FAILED_TESTS}${NC}"
    
    if [ $FAILED_TESTS -eq 0 ]; then
        echo -e "${GREEN}🎉 All tests passed!${NC}"
        exit 0
    else
        echo -e "${RED}❌ Some tests failed${NC}"
        exit 1
    fi
}

# Help function
show_help() {
    echo "Manual Test Script for Get Template by Country and Party Code Endpoint"
    echo ""
    echo "Usage: $0 [base_url]"
    echo ""
    echo "Arguments:"
    echo "  base_url    Base URL of the API server (default: http://localhost:8080)"
    echo ""
    echo "Examples:"
    echo "  $0                                    # Test against localhost:8080"
    echo "  $0 http://localhost:3000              # Test against localhost:3000"
    echo "  $0 https://api.example.com            # Test against remote server"
    echo ""
    echo "Prerequisites:"
    echo "  - API server must be running"
    echo "  - curl must be installed"
    echo "  - jq is recommended for JSON formatting"
    echo ""
}

# Check for help flag
if [ "$1" = "-h" ] || [ "$1" = "--help" ]; then
    show_help
    exit 0
fi

# Run main function
main
